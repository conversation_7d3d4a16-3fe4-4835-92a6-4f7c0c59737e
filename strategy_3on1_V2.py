# 2024/08/01  止损卖出修改为跌破5日均线
# Ptrade平台适配版本

import datetime as dt
import pandas as pd
import numpy as np
from datetime import datetime
from datetime import timedelta
import math
import pickle  # 导入pickle模块用于持久化处理


def initialize(context):
    # 获取研究路径，用于持久化文件存储
    g.notebook_path = get_research_path()

    # 尝试加载持久化数据
    try:
        # 使用研究路径，不使用os.path.join
        with open(g.notebook_path + 'strategy_3on1_state.pkl', 'rb') as f:
            saved_data = pickle.load(f)
            # 恢复保存的全局变量
            g.target_list = saved_data.get('target_list', [])
            g.target_list2 = saved_data.get('target_list2', [])
            log.info("成功加载持久化数据")
    except Exception as e:
        # 如果加载失败，初始化默认值
        g.target_list = []
        g.target_list2 = []
        log.info("未找到持久化数据或加载失败: %s" % str(e))

    # 一进二
    run_daily(context, get_stock_list, time='09:01')
    run_daily(context, buy, time='09:26')
    run_daily(context, sell, time='11:25')
    run_daily(context, sell, time='14:50')

    # 首版低开
    # run_daily(context, buy2, time='09:27') #9:25分知道开盘价后可以提前下单


# 选股
def get_stock_list(context):
    log.info("=== 开始选股流程 ===")
    # 获取前一交易日
    trading_day = get_trading_day()
    date = get_shifted_date(trading_day, -1, 'T')
    date_1 = get_shifted_date(date, -1, 'T')
    date_2 = get_shifted_date(date, -2, 'T')
    log.info("当前交易日: %s, 前一交易日: %s, 前二交易日: %s, 前三交易日: %s" % (trading_day, date, date_1, date_2))

    # 初始列表
    log.info("开始准备初始股票池...")
    initial_list = prepare_stock_list(date)
    log.info("初始股票池数量: %d" % len(initial_list))

    # 昨日涨停
    log.info("开始筛选昨日涨停股票...")
    hl_list = get_hl_stock(initial_list, date)
    log.info("昨日涨停股票数量: %d" % len(hl_list))
    if len(hl_list) > 0:
        log.info("昨日涨停股票前10只: %s" % str(hl_list[:10]))

    # 前日曾涨停
    log.info("开始筛选前日曾涨停股票...")
    hl1_list = get_ever_hl_stock(initial_list, date_1)
    log.info("前日曾涨停股票数量: %d" % len(hl1_list))

    # 前前日曾涨停
    log.info("开始筛选前前日曾涨停股票...")
    hl2_list = get_ever_hl_stock(initial_list, date_2)
    log.info("前前日曾涨停股票数量: %d" % len(hl2_list))

    # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
    elements_to_remove = set(hl1_list + hl2_list)
    log.info("需要从昨日涨停中剔除的股票数量: %d" % len(elements_to_remove))

    # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]
    log.info("过滤后的一进二候选股票数量: %d" % len(hl_list))
    if len(hl_list) > 0:
        log.info("一进二候选股票: %s" % str(hl_list))

    g.target_list = hl_list

    # 昨日曾涨停
    log.info("开始筛选弱转强候选股票...")
    h1_list = get_ever_hl_stock2(initial_list, date)
    log.info("昨日曾涨停但收盘未涨停股票数量: %d" % len(h1_list))

    # 上上个交易日涨停过滤
    elements_to_remove = get_hl_stock(initial_list, date_1)
    log.info("需要从弱转强候选中剔除的前日涨停股票数量: %d" % len(elements_to_remove))

    # 过滤上上个交易日涨停、曾涨停
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]
    log.info("过滤后的弱转强候选股票数量: %d" % len(all_list))
    if len(all_list) > 0:
        log.info("弱转强候选股票前10只: %s" % str(all_list[:10]))

    g.target_list2 = all_list
    log.info("=== 选股流程完成 ===")


# 交易
def buy(context):
    # 先进行择时判断
    log.info('开始进行择时判断...')
    timing_result = select_timing(context)
    log.info('择时判断结果: %s' % timing_result)

    if not timing_result:
        log.info('今日择时信号不满足，不进行交易')
        return

    log.info('择时信号满足，开始选股...')
    qualified_stocks = []
    gk_stocks = []
    dk_stocks = []
    rzq_stocks = []

    # 获取当前日期
    date_now = get_trading_day()
    # 确保日期是字符串格式
    if not isinstance(date_now, str):
        date_now = date_now.strftime('%Y-%m-%d')
    mid_time1 = ' 09:15:00'
    end_times1 = ' 09:26:00'
    start = date_now + mid_time1
    end = date_now + end_times1

    # 高开
    log.info("=== 开始筛选一进二股票 ===")
    log.info("一进二候选股票数量: %d" % len(g.target_list))

    for i, s in enumerate(g.target_list):
        log.info("--- 检查一进二股票 %d/%d: %s ---" % (i+1, len(g.target_list), s))

        try:
            # 条件一：均价，金额，市值，换手率
            log.info("检查条件一：均价、金额、市值、换手率...")
            prev_day_data = get_price(s, count=1, end_date=get_shifted_date(date_now, -1, 'T'), fields=['close', 'volume', 'money'], frequency='daily')
            avg_price_increase_value = prev_day_data['money'][0] / prev_day_data['volume'][0] / prev_day_data['close'][0] * 1.1 - 1
            log.info("均价增长值: %.4f, 成交金额: %.2e" % (avg_price_increase_value, prev_day_data['money'][0]))

            if avg_price_increase_value < 0.07:
                log.info("均价增长值不足0.07，跳过")
                continue
            if prev_day_data['money'][0] < 5.5e8:
                log.info("成交金额小于5.5亿，跳过")
                continue
            if prev_day_data['money'][0] > 20e8:
                log.info("成交金额大于20亿，跳过")
                continue

            # market_cap 总市值(亿元) > 70亿 流通市值(亿元) < 520亿
            log.info("检查市值和换手率...")
            turnover_ratio_data = get_fundamentals(s, 'valuation', ['turnover_ratio', 'market_cap', 'circulating_market_cap'], date=get_shifted_date(date_now, -1, 'T'))
            if turnover_ratio_data.empty:
                log.info("无法获取基本面数据，跳过")
                continue

            log.info("总市值: %.2f亿, 流通市值: %.2f亿" % (turnover_ratio_data['market_cap'][0], turnover_ratio_data['circulating_market_cap'][0]))
            if turnover_ratio_data['market_cap'][0] < 70:
                log.info("总市值小于70亿，跳过")
                continue
            if turnover_ratio_data['circulating_market_cap'][0] > 520:
                log.info("流通市值大于520亿，跳过")
                continue

            # 条件二：左压
            log.info("检查左压条件...")
            zyts = calculate_zyts(s, context)
            log.info("左压天数: %d" % zyts)
            volume_data = get_price(s, count=zyts, end_date=get_shifted_date(date_now, -1, 'T'), fields=['volume'], frequency='daily')
            if len(volume_data) < 2:
                log.info("成交量数据不足，跳过")
                continue

            max_prev_volume = max(volume_data['volume'][:-1])
            last_volume = volume_data['volume'][-1]
            log.info("昨日成交量: %d, 前期最大成交量: %d, 比值: %.4f" % (last_volume, max_prev_volume, last_volume / max_prev_volume))

            if last_volume <= max_prev_volume * 0.9:
                log.info("昨日成交量未突破前期最大值的90%，跳过")
                continue

            # 条件三：高开，开比
            log.info("检查高开和开比条件...")
            auction_data = get_trend_data(s, start_date=start, end_date=end, fields=['time', 'volume', 'current'])
            if auction_data.empty:
                log.info("无法获取集合竞价数据，跳过")
                continue

            # 获取涨停价
            stock_info = get_stock_info(s)
            high_limit = stock_info['close'] * 1.1
            log.info("昨收价: %.2f, 涨停价: %.2f" % (stock_info['close'], high_limit))

            auction_volume_ratio = auction_data['volume'][0] / volume_data['volume'][-1]
            log.info("集合竞价成交量: %d, 昨日成交量: %d, 开比: %.4f" % (auction_data['volume'][0], volume_data['volume'][-1], auction_volume_ratio))

            if auction_volume_ratio < 0.03:
                log.info("开比小于0.03，跳过")
                continue

            current_ratio = auction_data['current'][0] / (high_limit/1.1)
            log.info("集合竞价价格: %.2f, 开盘比例: %.4f" % (auction_data['current'][0], current_ratio))

            if current_ratio <= 1:
                log.info("开盘比例不大于1，跳过")
                continue
            if current_ratio >= 1.06:
                log.info("开盘比例大于等于1.06，跳过")
                continue

            # 如果股票满足所有条件，则添加到列表中
            log.info("*** %s 满足一进二所有条件，加入选股列表 ***" % s)
            gk_stocks.append(s)
            qualified_stocks.append(s)

        except Exception as e:
            log.info("检查股票 %s 时出错: %s" % (s, str(e)))
            continue

    log.info("一进二筛选完成，符合条件股票数量: %d" % len(gk_stocks))

    # 低开
    log.info("=== 开始筛选首板低开股票 ===")
    # 基础信息
    date = get_shifted_date(date_now, -1, 'T')
    log.info("使用日期: %s" % date)

    # 昨日涨停列表
    log.info("准备首板低开初始股票池...")
    initial_list = prepare_stock_list2(date)
    log.info("首板低开初始股票池数量: %d" % len(initial_list))

    hl_list = get_hl_stock(initial_list, date)
    log.info("昨日涨停股票数量: %d" % len(hl_list))

    if len(hl_list) != 0:
        # 获取非连板涨停的股票
        log.info("筛选非连板涨停股票...")
        ccd = get_continue_count_df(hl_list, date, 10)
        lb_list = list(ccd.index)
        log.info("连板股票数量: %d" % len(lb_list))

        stock_list = [s for s in hl_list if s not in lb_list]
        log.info("非连板涨停股票数量: %d" % len(stock_list))

        # 计算相对位置
        log.info("计算相对位置...")
        rpd = get_relative_position_df(stock_list, date, 60)
        log.info("获取到相对位置数据的股票数量: %d" % len(rpd))

        rpd = rpd[rpd['rp'] <= 0.5]
        stock_list = list(rpd.index)
        log.info("相对位置<=0.5的股票数量: %d" % len(stock_list))

        # 低开
        log.info("检查低开条件...")
        df = pd.DataFrame()
        if len(stock_list) != 0:
            df = get_price(stock_list, end_date=date, frequency='daily', fields=['close'], count=1, panel=False).set_index('code')
            log.info("获取到昨收价数据的股票数量: %d" % len(df))

            # 获取当前开盘价
            valid_count = 0
            for s in stock_list:
                try:
                    snapshot = get_snapshot(s)
                    df.loc[s, 'open_pct'] = snapshot['open'] / df.loc[s, 'close']
                    valid_count += 1
                except Exception as e:
                    log.info("获取股票 %s 开盘价失败: %s" % (s, str(e)))
                    continue

            log.info("成功获取开盘价的股票数量: %d" % valid_count)

            # 筛选低开股票
            before_filter = len(df)
            df = df[(0.955 <= df['open_pct']) & (df['open_pct'] <= 0.97)]  # 低开越多风险越大，选择3个多点即可
            stock_list = list(df.index)
            log.info("低开筛选前: %d, 筛选后: %d" % (before_filter, len(stock_list)))

        # 检查成交金额条件
        log.info("检查成交金额条件...")
        for i, s in enumerate(stock_list):
            log.info("--- 检查首板低开股票 %d/%d: %s ---" % (i+1, len(stock_list), s))
            try:
                prev_day_data = get_price(s, count=1, end_date=date, fields=['close', 'volume', 'money'], frequency='daily')
                log.info("成交金额: %.2e" % prev_day_data['money'][0])

                if prev_day_data['money'][0] >= 1e8:
                    log.info("*** %s 满足首板低开所有条件，加入选股列表 ***" % s)
                    dk_stocks.append(s)
                    qualified_stocks.append(s)
                else:
                    log.info("成交金额小于1亿，跳过")
            except Exception as e:
                log.info("检查股票 %s 时出错: %s" % (s, str(e)))
                continue
    else:
        log.info("昨日无涨停股票，跳过首板低开策略")

    log.info("首板低开筛选完成，符合条件股票数量: %d" % len(dk_stocks))

    # 弱转强
    log.info("=== 开始筛选弱转强股票 ===")
    log.info("弱转强候选股票数量: %d" % len(g.target_list2))

    for i, s in enumerate(g.target_list2):
        log.info("--- 检查弱转强股票 %d/%d: %s ---" % (i+1, len(g.target_list2), s))

        try:
            # 过滤前面三天涨幅超过28%的票
            log.info("检查前三天涨幅...")
            price_data = get_price(s, count=4, end_date=get_shifted_date(date_now, -1, 'T'), fields=['close'], frequency='daily')
            if len(price_data) < 4:
                log.info("价格数据不足4天，跳过")
                continue

            increase_ratio = (price_data['close'][-1] - price_data['close'][0]) / price_data['close'][0]
            log.info("前三天涨幅: %.4f" % increase_ratio)

            if increase_ratio > 0.28:
                log.info("前三天涨幅超过28%，跳过")
                continue

            # 过滤前一日收盘价小于开盘价5%以上的票
            log.info("检查前一日开收盘比...")
            prev_day_data = get_price(s, count=1, end_date=get_shifted_date(date_now, -1, 'T'), fields=['open', 'close'], frequency='daily')
            if len(prev_day_data) < 1:
                log.info("无法获取前一日开收盘数据，跳过")
                continue

            open_close_ratio = (prev_day_data['close'][0] - prev_day_data['open'][0]) / prev_day_data['open'][0]
            log.info("前一日开收盘比: %.4f" % open_close_ratio)

            if open_close_ratio < -0.05:
                log.info("前一日收盘价小于开盘价5%以上，跳过")
                continue

            # 检查均价、金额条件
            log.info("检查均价和成交金额...")
            prev_day_data = get_price(s, count=1, end_date=get_shifted_date(date_now, -1, 'T'), fields=['close', 'volume', 'money'], frequency='daily')
            avg_price_increase_value = prev_day_data['money'][0] / prev_day_data['volume'][0] / prev_day_data['close'][0] - 1
            log.info("均价增长值: %.4f, 成交金额: %.2e" % (avg_price_increase_value, prev_day_data['money'][0]))

            if avg_price_increase_value < -0.04:
                log.info("均价增长值小于-0.04，跳过")
                continue
            if prev_day_data['money'][0] < 3e8:
                log.info("成交金额小于3亿，跳过")
                continue
            if prev_day_data['money'][0] > 19e8:
                log.info("成交金额大于19亿，跳过")
                continue

            # 检查市值条件
            log.info("检查市值条件...")
            turnover_ratio_data = get_fundamentals(s, 'valuation', ['turnover_ratio', 'market_cap', 'circulating_market_cap'], date=get_shifted_date(date_now, -1, 'T'))
            if turnover_ratio_data.empty:
                log.info("无法获取基本面数据，跳过")
                continue

            log.info("总市值: %.2f亿, 流通市值: %.2f亿" % (turnover_ratio_data['market_cap'][0], turnover_ratio_data['circulating_market_cap'][0]))

            if turnover_ratio_data['market_cap'][0] < 70:
                log.info("总市值小于70亿，跳过")
                continue
            if turnover_ratio_data['circulating_market_cap'][0] > 520:
                log.info("流通市值大于520亿，跳过")
                continue

            # 检查左压条件
            log.info("检查左压条件...")
            zyts = calculate_zyts(s, context)
            log.info("左压天数: %d" % zyts)
            volume_data = get_price(s, count=zyts, end_date=get_shifted_date(date_now, -1, 'T'), fields=['volume'], frequency='daily')
            if len(volume_data) < 2:
                log.info("成交量数据不足，跳过")
                continue

            max_prev_volume = max(volume_data['volume'][:-1])
            last_volume = volume_data['volume'][-1]
            log.info("昨日成交量: %d, 前期最大成交量: %d, 比值: %.4f" % (last_volume, max_prev_volume, last_volume / max_prev_volume))

            if last_volume <= max_prev_volume * 0.9:
                log.info("昨日成交量未突破前期最大值的90%，跳过")
                continue

            # 检查集合竞价条件
            log.info("检查集合竞价条件...")
            auction_data = get_trend_data(s, start_date=start, end_date=end, fields=['time', 'volume', 'current'])
            if auction_data.empty:
                log.info("无法获取集合竞价数据，跳过")
                continue

            # 获取涨停价
            stock_info = get_stock_info(s)
            high_limit = stock_info['close'] * 1.1
            log.info("昨收价: %.2f, 涨停价: %.2f" % (stock_info['close'], high_limit))

            auction_volume_ratio = auction_data['volume'][0] / volume_data['volume'][-1]
            log.info("集合竞价成交量: %d, 昨日成交量: %d, 开比: %.4f" % (auction_data['volume'][0], volume_data['volume'][-1], auction_volume_ratio))

            if auction_volume_ratio < 0.03:
                log.info("开比小于0.03，跳过")
                continue

            current_ratio = auction_data['current'][0] / (high_limit/1.1)
            log.info("集合竞价价格: %.2f, 开盘比例: %.4f" % (auction_data['current'][0], current_ratio))

            if current_ratio <= 0.98:
                log.info("开盘比例不大于0.98，跳过")
                continue
            if current_ratio >= 1.09:
                log.info("开盘比例大于等于1.09，跳过")
                continue

            # 如果股票满足所有条件，则添加到列表中
            log.info("*** %s 满足弱转强所有条件，加入选股列表 ***" % s)
            rzq_stocks.append(s)
            qualified_stocks.append(s)

        except Exception as e:
            log.info("检查股票 %s 时出错: %s" % (s, str(e)))
            continue

    log.info("弱转强筛选完成，符合条件股票数量: %d" % len(rzq_stocks))

    if len(qualified_stocks) > 0:
        log.info('———————————————————————————————————')
        log.info('今日选股：%s' % ','.join(qualified_stocks))
        log.info('一进二：%s' % ','.join(gk_stocks))
        log.info('首板低开：%s' % ','.join(dk_stocks))
        log.info('弱转强：%s' % ','.join(rzq_stocks))
        log.info('今日选股：%s' % ','.join(qualified_stocks))
        log.info('———————————————————————————————————')
    else:
        log.info('今日无目标个股')

    # 检查可用资金比例，与原策略保持一致
    if len(qualified_stocks) != 0 and context.portfolio.available_cash / context.portfolio.total_value > 0.3:
        # 按照原策略逻辑，平均分配资金
        value = context.portfolio.available_cash / len(qualified_stocks)

        for s in qualified_stocks:
            # 获取最新价格
            current_price = get_snapshot(s)['last']

            # 检查资金是否足够买入至少100股
            if context.portfolio.available_cash / current_price > 100:
                # 使用市价下单
                order_value(s, value)
                log.info('买入 %s' % s)
                log.info('———————————————————————————————————')


def sell(context):
    """
    卖出逻辑 - 与原策略保持一致
    """
    # 获取当前时间
    current_time = str(context.current_dt)[-8:]

    # 根据时间执行不同的卖出策略
    if current_time == '11:25:00':
        for s in list(context.portfolio.positions):
            # 获取最新价格
            current_price = get_snapshot(s)['last']
            # 获取涨停价
            stock_info = get_stock_info(s)
            high_limit = stock_info['close'] * 1.1

            # 止盈条件：非涨停且有盈利
            if ((context.portfolio.positions[s].closeable_amount != 0) and
                (current_price < high_limit) and
                (current_price > 1 * context.portfolio.positions[s].avg_cost)):
                order_target(s, 0)
                log.info('止盈卖出 %s' % s)
                log.info('———————————————————————————————————')

    elif current_time == '14:50:00':
        for s in list(context.portfolio.positions):
            # 获取最新价格
            current_price = get_snapshot(s)['last']
            # 获取涨停价
            stock_info = get_stock_info(s)
            high_limit = stock_info['close'] * 1.1

            # 计算5日均线
            close_data = get_price(s, count=4, end_date=get_shifted_date(get_trading_day(), -1, 'T'),
                                 fields=['close'], frequency='daily')
            M4 = close_data['close'].mean()
            MA5 = (M4 * 4 + current_price) / 5

            # 止盈条件：非涨停且有盈利
            if ((context.portfolio.positions[s].closeable_amount != 0) and
                (current_price < high_limit) and
                (current_price > 1 * context.portfolio.positions[s].avg_cost)):
                order_target(s, 0)
                log.info('止盈卖出 %s' % s)
                log.info('———————————————————————————————————')
            # 止损条件：价格低于5日均线
            elif ((context.portfolio.positions[s].closeable_amount != 0) and
                  (current_price < MA5)):
                order_target(s, 0)
                log.info('止损卖出 %s' % s)
                log.info('———————————————————————————————————')


# 处理日期相关函数
def transform_date(date, date_type):
    """
    将日期在字符串格式和datetime格式之间转换
    """
    if date_type == 'str':
        if isinstance(date, str):
            return date
        elif isinstance(date, datetime):
            return date.strftime('%Y-%m-%d')
        elif hasattr(date, 'strftime'):  # 处理 datetime.date 类型
            return date.strftime('%Y-%m-%d')
    elif date_type == 'datetime':
        if isinstance(date, datetime):
            return date
        elif isinstance(date, str):
            return datetime.strptime(date, '%Y-%m-%d')
        elif hasattr(date, 'strftime'):  # 处理 datetime.date 类型
            return datetime.combine(date, datetime.min.time())
    return date


def get_shifted_date(date, days, days_type='T'):
    """
    获取日期之前或之后的日期，可按交易日或自然日偏移
    参数：
        date：开始日期（str或datetime）
        days：偏移的天数，正为之后，负为之前
        days_type：类型 'T'交易日, 'D'自然日
    """
    date = transform_date(date, 'str')
    if days_type == 'D':
        # 按自然日偏移
        dt_date = transform_date(date, 'datetime')
        shifted_date = dt_date + timedelta(days=days)
        return transform_date(shifted_date, 'str')
    elif days_type == 'T':
        # 按交易日偏移
        if days > 0:
            trade_days = get_trade_days(start_date=date, count=days+1)
            if len(trade_days) == days+1:
                result = trade_days[-1]
                # 确保返回字符串格式
                if hasattr(result, 'strftime'):
                    return result.strftime('%Y-%m-%d')
                return str(result)
            else:
                return date
        elif days < 0:
            days = abs(days)
            trade_days = get_trade_days(end_date=date, count=days+1)
            if len(trade_days) == days+1:
                result = trade_days[0]
                # 确保返回字符串格式
                if hasattr(result, 'strftime'):
                    return result.strftime('%Y-%m-%d')
                return str(result)
            else:
                return date
        else:
            return date


# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    """
    过滤掉上市不足指定天数的新股
    """
    filtered_list = []
    for stock in initial_list:
        stock_info = get_stock_info(stock)
        list_date = stock_info.get('list_date', '')
        if list_date and pd.to_datetime(list_date) < pd.to_datetime(date) - pd.Timedelta(days=days):
            filtered_list.append(stock)
    return filtered_list


def filter_st_stock(initial_list, date):
    """
    过滤ST股票
    """
    filtered_list = []
    for stock in initial_list:
        stock_name = get_stock_name(stock)
        if 'ST' not in stock_name and '*' not in stock_name:
            filtered_list.append(stock)
    return filtered_list


def filter_kcbj_stock(initial_list):
    """
    过滤科创板、北交所股票
    """
    filtered_list = [stock for stock in initial_list if not (stock.startswith('688') or stock.startswith('689') or stock.endswith('.BJ'))]
    return filtered_list


def filter_paused_stock(initial_list, date):
    """
    过滤停牌股票
    """
    filtered_list = []
    for stock in initial_list:
        status_info = get_stock_status(stock, date)
        if status_info.get('paused', 1) == 0:  # 0表示未停牌
            filtered_list.append(stock)
    return filtered_list


# 一字
def filter_extreme_limit_stock(stock_list, date):
    """
    过滤一字涨停股票
    """
    filtered_list = []
    for stock in stock_list:
        price_data = get_price(stock, count=1, end_date=date, fields=['high', 'low'], frequency='daily')
        if price_data['high'][0] != price_data['low'][0]:  # 非一字板
            filtered_list.append(stock)
    return filtered_list


# 每日初始股票池
def prepare_stock_list(date):
    """
    准备初始股票池
    """
    # 获取所有A股
    # get_Ashares需要YYYYmmdd格式的日期
    date_formatted = date.replace('-', '')
    initial_list = get_Ashares(date_formatted)
    # 过滤新股
    initial_list = filter_new_stock(initial_list, date)
    # 过滤ST股
    initial_list = filter_st_stock(initial_list, date)
    # 过滤科创板、北交所
    initial_list = filter_kcbj_stock(initial_list)
    # 过滤停牌
    initial_list = filter_paused_stock(initial_list, date)

    return initial_list


# 计算左压天数
def calculate_zyts(stock, context):
    """
    计算左压天数 - 与原策略保持一致
    """
    date = get_shifted_date(get_trading_day(), -1, 'T')
    # 获取历史高价数据
    price_data = get_price(stock, count=101, end_date=date, fields=['high'], frequency='daily')
    if len(price_data) < 10:  # 数据不足时返回默认值
        return 20

    high_prices = price_data['high']
    prev_high = high_prices.iloc[-1]

    # 查找前面的高点
    zyts_0 = 100  # 默认值
    for i in range(2, len(high_prices)-1):
        if high_prices.iloc[-i-1] >= prev_high:
            zyts_0 = i-1
            break

    zyts = zyts_0 + 5
    return zyts


# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    """
    获取指定日期涨停的股票列表
    """
    # 批量获取数据，提高效率
    if not initial_list:
        return []

    # 分批处理，避免一次请求数据过多
    batch_size = 100
    hl_list = []

    for i in range(0, len(initial_list), batch_size):
        batch_stocks = initial_list[i:i+batch_size]
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['close', 'high_limit'], count=1, panel=False)

        # 处理数据
        if not df.empty:
            df = df.dropna()  # 去除停牌
            df = df[df['close'] >= df['high_limit'] * 0.999]  # 涨停判断
            if 'code' in df.columns:
                batch_hl_list = list(df['code'])
                hl_list.extend(batch_hl_list)

    return hl_list


# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    """
    获取指定日期内盘中曾涨停的股票列表
    """
    # 批量获取数据，提高效率
    if not initial_list:
        return []

    # 分批处理，避免一次请求数据过多
    batch_size = 100
    hl_list = []

    for i in range(0, len(initial_list), batch_size):
        batch_stocks = initial_list[i:i+batch_size]
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['high', 'high_limit'], count=1, panel=False)

        # 处理数据
        if not df.empty:
            df = df.dropna()  # 去除停牌
            df = df[df['high'] >= df['high_limit'] * 0.999]  # 曾涨停判断
            if 'code' in df.columns:
                batch_hl_list = list(df['code'])
                hl_list.extend(batch_hl_list)

    return hl_list


# 筛选曾涨停
def get_ever_hl_stock2(initial_list, date):
    """
    获取指定日期内盘中曾涨停但收盘未涨停的股票列表
    """
    # 批量获取数据，提高效率
    if not initial_list:
        return []

    # 分批处理，避免一次请求数据过多
    batch_size = 100
    hl_list = []

    for i in range(0, len(initial_list), batch_size):
        batch_stocks = initial_list[i:i+batch_size]
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['close', 'high', 'high_limit'], count=1, panel=False)

        # 处理数据
        if not df.empty:
            df = df.dropna()  # 去除停牌
            # 高点涨停但收盘未涨停
            cd1 = df['high'] >= df['high_limit'] * 0.999
            cd2 = df['close'] < df['high_limit'] * 0.999
            df = df[cd1 & cd2]

            if 'code' in df.columns:
                batch_hl_list = list(df['code'])
                hl_list.extend(batch_hl_list)

    return hl_list


# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    """
    计算股票在指定日期前的涨停次数和一字涨停次数
    """
    if not hl_list:
        return pd.DataFrame(columns=['count', 'extreme_count'])

    # 批量获取数据
    batch_size = 50
    result_df = pd.DataFrame()

    for i in range(0, len(hl_list), batch_size):
        batch_stocks = hl_list[i:i+batch_size]

        # 获取历史数据
        df = get_price(batch_stocks, end_date=date, frequency='daily',
                      fields=['close', 'high_limit', 'low'], count=watch_days, panel=False)

        if df.empty:
            continue

        # 按股票代码分组处理
        for stock in batch_stocks:
            stock_data = df[df['code'] == stock]
            if stock_data.empty:
                continue

            # 计算涨停次数和一字涨停次数
            hl_days = sum(stock_data['close'] >= stock_data['high_limit'] * 0.999)
            extreme_hl_days = sum(stock_data['low'] >= stock_data['high_limit'] * 0.999)

            # 添加到结果
            result_df = result_df.append({
                'code': stock,
                'count': hl_days,
                'extreme_count': extreme_hl_days
            }, ignore_index=True)

    # 设置索引并排序
    if not result_df.empty:
        result_df = result_df.set_index('code')
        result_df = result_df.sort_values('count', ascending=False)

    return result_df


# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    """
    计算股票在指定日期前的连续涨停次数
    """
    if not hl_list:
        return pd.DataFrame(columns=['count', 'extreme_count'])

    # 创建空的DataFrame用于存储结果
    df = pd.DataFrame()

    # 对每个涨停天数进行计算
    for d in range(2, watch_days+1):
        # 获取涨停次数
        HLC = get_hl_count_df(hl_list, date, d)
        # 筛选出涨停次数等于d的股票（连续涨停）
        CHLC = HLC[HLC['count'] == d]
        # 添加到结果DataFrame
        df = df.append(CHLC)

    # 处理可能有多条记录的股票，保留最大连板数
    stock_list = list(set(df.index))
    ccd = pd.DataFrame()

    for s in stock_list:
        try:
            tmp = df.loc[[s]]
            if len(tmp) > 1:
                # 如果有多条记录，取最大连板数
                M = tmp['count'].max()
                tmp = tmp[tmp['count'] == M]
            ccd = ccd.append(tmp)
        except:
            continue

    # 按连板数排序
    if len(ccd) != 0:
        ccd = ccd.sort_values(by='count', ascending=False)

    return ccd


# 计算昨涨幅
def get_index_increase_ratio(index_code, context):
    """
    获取指数昨日涨幅
    """
    date = get_trading_day()
    prev_date = get_shifted_date(date, -1, 'T')
    prev_prev_date = get_shifted_date(prev_date, -1, 'T')

    # 获取前一日和前前一日收盘价
    price_data = get_price(index_code, count=2, end_date=prev_date, fields=['close'], frequency='daily')
    prev_close = price_data['close'][-1]
    prev_prev_close = price_data['close'][0]

    # 计算涨幅
    return (prev_close - prev_prev_close) / prev_prev_close


# 获取股票所属行业
def getStockIndustry(stocks):
    """
    获取股票所属行业
    """
    industry_dict = {}
    for stock in stocks:
        # 在Ptrade中没有直接获取股票行业的API，此处使用股票板块信息代替
        block_info = get_stock_blocks(stock)
        if block_info and len(block_info) > 0:
            industry = block_info[0]  # 取第一个板块作为行业
            industry_dict[stock] = industry
        else:
            industry_dict[stock] = '未知'
    return industry_dict


# 获取市场宽度
def get_market_breadth(context):
    """
    获取市场宽度 - 计算行业偏离度
    使用所有A股替代中证全指成分股
    """
    try:
        log.info("开始计算市场宽度...")
        # 获取前一交易日
        yesterday = get_shifted_date(get_trading_day(), -1, 'T')
        log.info("使用日期: %s" % yesterday)

        # 获取所有A股作为初始列表，替代中证全指
        # get_Ashares需要YYYYmmdd格式的日期
        yesterday_formatted = yesterday.replace('-', '')
        stocks = get_Ashares(yesterday_formatted)
        # 过滤新股、ST股等
        stocks = filter_new_stock(stocks, yesterday)
        stocks = filter_st_stock(stocks, yesterday)
        stocks = filter_kcbj_stock(stocks)
        stocks = filter_paused_stock(stocks, yesterday)

        log.info("获取到 %d 只股票" % len(stocks))

        # 获取股票价格数据
        count = 21  # 20日均线 + 当日
        all_data = pd.DataFrame()

        # 分批获取数据
        batch_size = 50
        for i in range(0, len(stocks), batch_size):
            batch_stocks = stocks[i:i+batch_size]
            h = get_price(batch_stocks, end_date=yesterday, frequency='daily',
                         fields=['close'], count=count, panel=False)
            if not h.empty:
                all_data = all_data.append(h)

        if all_data.empty:
            log.info("未获取到有效数据")
            return ""

        # 处理数据
        all_data['date'] = pd.to_datetime(all_data['time']).dt.date
        df_close = all_data.pivot(index='code', columns='date', values='close').dropna(axis=0)
        log.info("有效数据股票数量: %d" % len(df_close))

        if len(df_close) < 10:
            log.info("有效数据不足")
            return ""

        # 计算20日均线
        df_ma20 = df_close.rolling(window=20, axis=1).mean().iloc[:, -1:]

        # 计算偏离程度（股价是否高于20日均线）
        df_bias = df_close.iloc[:, -1:] > df_ma20

        # 获取股票所属行业
        industry_dict = getStockIndustry(list(df_bias.index))
        log.info("获取到 %d 只股票的行业信息" % len(industry_dict))

        # 将行业信息添加到df_bias
        industry_series = pd.Series(industry_dict)
        df_bias['industry_name'] = industry_series

        # 去除没有行业信息的股票
        df_bias = df_bias.dropna(subset=['industry_name'])
        log.info("有效行业信息股票数量: %d" % len(df_bias))

        if len(df_bias) < 10:
            log.info("有效行业数据不足")
            return ""

        # 计算行业偏离比例
        last_date = df_bias.columns[0]
        if isinstance(last_date, datetime):
            last_date = last_date.date()

        # 按行业分组计算偏离度
        grouped = df_bias.groupby('industry_name')
        industry_counts = grouped.size()
        industry_above_ma = grouped[last_date].sum()

        # 计算每个行业高于均线的比例
        industry_ratios = (industry_above_ma * 100.0 / industry_counts).round()

        # 获取偏离程度最高的行业
        if len(industry_ratios) > 0:
            top_industry = industry_ratios.nlargest(1).index[0]
            log.info("市场宽度计算结果 - 领先行业Top1: %s" % top_industry)
            return top_industry
        else:
            return ""
    except Exception as e:
        log.info("市场宽度计算失败: %s" % str(e))
        # 出错时返回空字符串
        return ""


# 择时判断
def select_timing(context):
    """
    择时判断函数 - 与原策略保持一致
    """
    log.info("开始执行择时判断函数...")
    try:
        # 获取市场宽度Top1行业
        top_industry = get_market_breadth(context)
        log.info("获取到的市场宽度领先行业Top1: %s" % top_industry)

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        log.info("需要监控的行业: %s" % str(restricted_industries))

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                log.info("Top1行业 '%s' 包含监控行业 '%s'" % (top_industry, industry))
                break

        if not is_restricted:
            log.info("Top1行业不在监控行业中，择时条件满足，可以交易")
            return True
        else:
            log.info("Top1行业在监控行业中，择时条件不满足，不进行交易")
            return False
    except Exception as e:
        log.info("择时判断出错: %s" % str(e))
        # 出错时默认允许交易
        return True


# 每日初始股票池2（用于首版低开策略）
def prepare_stock_list2(date):
    """
    准备初始股票池
    """
    # 获取所有A股
    # get_Ashares需要YYYYmmdd格式的日期
    date_formatted = date.replace('-', '')
    initial_list = get_Ashares(date_formatted)
    # 过滤新股
    initial_list = filter_new_stock(initial_list, date, days=250)
    # 过滤ST股
    initial_list = filter_st_stock(initial_list, date)
    # 过滤科创板、北交所
    initial_list = filter_kcbj_stock(initial_list)
    # 过滤停牌
    initial_list = filter_paused_stock(initial_list, date)

    return initial_list


# 计算股票处于一段时间内相对位置
def get_relative_position_df(stock_list, date, watch_days):
    """
    计算股票在一段时间内的相对位置
    """
    if len(stock_list) == 0:
        return pd.DataFrame(columns=['rp'])

    # 批量获取数据
    batch_size = 50
    all_data = pd.DataFrame()

    for i in range(0, len(stock_list), batch_size):
        batch_stocks = stock_list[i:i+batch_size]
        # 获取历史数据
        df = get_price(batch_stocks, end_date=date, fields=['high', 'low', 'close'],
                      count=watch_days, frequency='daily', panel=False)

        if not df.empty:
            all_data = all_data.append(df)

    if all_data.empty:
        return pd.DataFrame(columns=['rp'])

    # 去除缺失值
    all_data = all_data.dropna()

    # 按股票代码分组
    grouped = all_data.groupby('code')

    # 计算每只股票的最后收盘价、最高价和最低价
    close = grouped.apply(lambda df: df['close'].iloc[-1])
    high = grouped.apply(lambda df: df['high'].max())
    low = grouped.apply(lambda df: df['low'].min())

    # 计算相对位置
    result = pd.DataFrame()
    result['rp'] = (close - low) / (high - low)

    # 处理可能的无效值
    result = result.replace([np.inf, -np.inf], np.nan).dropna()

    return result


# 持久化保存函数
def save_strategy_state(context):
    """
    将策略状态保存到文件，用于策略重启后恢复
    """
    try:
        # 创建要保存的数据字典
        save_data = {
            'target_list': g.target_list,
            'target_list2': g.target_list2,
            # 可以添加其他需要保存的全局变量
        }

        # 保存到文件
        with open(g.notebook_path + 'strategy_3on1_state.pkl', 'wb') as f:
            pickle.dump(save_data, f, -1)  # -1表示使用最高的协议版本

        log.info("策略状态已成功保存")
    except Exception as e:
        log.error("保存策略状态失败: %s" % str(e))

# 盘后处理函数
def after_trading_end(context, data):
    """
    每日收盘后处理函数，用于执行收盘后的操作
    """
    log.info("执行盘后处理...")

    # 保存策略状态
    save_strategy_state(context)

    # 记录当日持仓情况
    positions_info = []
    for security, position in context.portfolio.positions.items():
        if position.amount > 0:
            positions_info.append(f"{security}: {position.amount}股, 成本: {position.avg_cost:.2f}")

    if positions_info:
        log.info("当日收盘持仓情况:")
        for info in positions_info:
            log.info(info)
    else:
        log.info("当日收盘无持仓")

    log.info("盘后处理完成")

# 处理函数
def handle_data(context, data):
    """
    策略主要逻辑已在run_daily中实现，此处作为占位函数
    """
    pass